import { useState } from 'react'
import type {
	TradingSettings,
	StrategyType,
	StrategyConfig,
	StopLossMode,
	StopLossConfig,
	ScreenshotConfig
} from '../../../../shared/types'

interface SettingsPanelProps {
	settings: TradingSettings
	onSave: (settings: TradingSettings) => void
	onCancel: () => void
}

const availableStrategies = [
	{
		type: 'threshold' as StrategyType,
		name: 'Threshold Strategy',
		description: 'Trades when price change exceeds a specified threshold percentage'
	},
	{
		type: 'moving-average' as StrategyType,
		name: 'Moving Average Crossover',
		description: 'Trades based on short-term and long-term moving average crossovers'
	},
	{
		type: 'rsi' as StrategyType,
		name: 'RSI Strategy',
		description: 'Trades based on Relative Strength Index (RSI) overbought/oversold levels'
	},
	{
		type: 'macd' as StrategyType,
		name: 'MACD Strategy',
		description: 'Trades based on MACD (Moving Average Convergence Divergence) signals'
	},
	{
		type: 'bollinger-bands' as StrategyType,
		name: 'Bollinger Bands Strategy',
		description: 'Trades based on Bollinger Bands breakouts and mean reversion'
	},
	{
		type: 'oscillate' as StrategyType,
		name: 'Oscillate Strategy',
		description: 'Trades within price ranges, buying at support and selling at resistance'
	},
	{
		type: 'slide' as StrategyType,
		name: 'Slide Strategy',
		description: 'Follows trending movements with dynamic stop-losses and momentum analysis'
	},
	{
		type: 'ai' as StrategyType,
		name: 'AI Strategy',
		description: 'Uses ChatGPT/DeepSeek AI for intelligent market analysis and trading decisions'
	},
	{
		type: 'custom' as StrategyType,
		name: 'Custom Strategy',
		description: 'User-defined custom trading strategy with editable code'
	}
]

export function SettingsPanel({ settings, onSave, onCancel }: SettingsPanelProps) {
	const [formData, setFormData] = useState<TradingSettings>(settings)

	const handleSubmit = (e: React.FormEvent) => {
		e.preventDefault()
		onSave(formData)
	}

	const handleInputChange = (field: keyof TradingSettings, value: unknown) => {
		setFormData(prev => ({
			...prev,
			[field]: value
		}))
	}

	const handleStrategyConfigChange = (field: keyof StrategyConfig, value: unknown) => {
		setFormData(prev => ({
			...prev,
			strategyConfig: {
				...prev.strategyConfig,
				[field]: value
			}
		}))
	}

	const handleStopLossConfigChange = (field: keyof StopLossConfig, value: unknown) => {
		setFormData(prev => ({
			...prev,
			stopLoss: {
				enabled: false,
				mode: 'percentage' as StopLossMode,
				percentage: 10,
				fixedAmount: 100,
				...prev.stopLoss,
				[field]: value
			}
		}))
	}

	const handleScreenshotConfigChange = (field: keyof ScreenshotConfig, value: unknown) => {
		setFormData(prev => ({
			...prev,
			screenshotConfig: {
				enabled: false,
				debugMode: false,
				...prev.screenshotConfig,
				[field]: value
			}
		}))
	}

	const handleStrategyChange = (strategyType: StrategyType) => {
		// Set default config for the selected strategy
		let defaultConfig: StrategyConfig = {}

		switch (strategyType) {
			case 'threshold':
				defaultConfig = { threshold: 0.02 }
				break
			case 'moving-average':
				defaultConfig = { shortPeriod: 5, longPeriod: 20 }
				break
			case 'rsi':
				defaultConfig = { rsiPeriod: 14, oversoldLevel: 30, overboughtLevel: 70 }
				break
			case 'macd':
				defaultConfig = { fastPeriod: 12, slowPeriod: 26, signalPeriod: 9 }
				break
			case 'bollinger-bands':
				defaultConfig = { period: 20, standardDeviations: 2 }
				break
			case 'oscillate':
				defaultConfig = {
					rangeThreshold: 0.5,
					minOscillations: 3,
					breakoutThreshold: 0.3,
					timeWindow: 15,
					confidenceThreshold: 0.7,
					maxPositions: 2
				}
				break
			case 'slide':
				defaultConfig = {
					trendThreshold: 0.2,
					momentumPeriod: 10,
					dynamicStopLoss: true,
					trailDistance: 0.15,
					minTrendStrength: 0.6,
					breakoutConfirmation: true
				}
				break
			case 'ai':
				defaultConfig = {
					aiProvider: 'openai',
					apiKey: '',
					minConfidence: 0.7,
					analysisInterval: 60,
					fallbackStrategy: 'momentum',
					riskManagement: true,
					maxDailyTrades: 20,
					enablePatternRecognition: true
				}
				break
			case 'custom':
				defaultConfig = { customCode: '' }
				break
		}

		setFormData(prev => ({
			...prev,
			strategy: strategyType,
			strategyConfig: defaultConfig
		}))
	}

	return (
		<div className="bg-gray-800 rounded-lg p-4">
			<h2 className="text-xl font-semibold mb-4">Trading Settings</h2>

			<form onSubmit={handleSubmit} className="space-y-4">
				{/* Strategy Selection */}
				<div className="border-b border-gray-700 pb-4">
					<h3 className="text-base font-medium text-gray-200 mb-3">Trading Strategy</h3>

					<div className="space-y-3">
						<div>
							<label className="block text-sm font-medium text-gray-300 mb-1">Strategy Type</label>
							<select
								value={formData.strategy}
								onChange={e => handleStrategyChange(e.target.value as StrategyType)}
								className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-blue-500 text-sm"
							>
								{availableStrategies.map(strategy => (
									<option key={strategy.type} value={strategy.type}>
										{strategy.name}
									</option>
								))}
							</select>
							<p className="text-xs text-gray-400 mt-1">
								{availableStrategies.find(s => s.type === formData.strategy)?.description}
							</p>
						</div>
					</div>
				</div>

				{/* Strategy Configuration */}
				<div className="border-b border-gray-700 pb-4">
					<h3 className="text-base font-medium text-gray-200 mb-3">Strategy Configuration</h3>

					{formData.strategy === 'threshold' && (
						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div>
								<label className="block text-sm font-medium text-gray-300 mb-1">Price Change Threshold (%)</label>
								<input
									type="number"
									step="0.01"
									min="0.01"
									max="10"
									value={formData.strategyConfig.threshold || 0.02}
									onChange={e => handleStrategyConfigChange('threshold', parseFloat(e.target.value))}
									className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-blue-500 text-sm"
									placeholder="0.02"
								/>
								<p className="text-xs text-gray-400 mt-1">Minimum price change percentage to trigger a trade</p>
							</div>
						</div>
					)}

					{formData.strategy === 'moving-average' && (
						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div>
								<label className="block text-sm font-medium text-gray-300 mb-1">Short Period</label>
								<input
									type="number"
									min="1"
									max="50"
									value={formData.strategyConfig.shortPeriod || 5}
									onChange={e => handleStrategyConfigChange('shortPeriod', parseInt(e.target.value))}
									className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-blue-500 text-sm"
									placeholder="5"
								/>
								<p className="text-xs text-gray-400 mt-1">Short-term moving average period</p>
							</div>
							<div>
								<label className="block text-sm font-medium text-gray-300 mb-1">Long Period</label>
								<input
									type="number"
									min="2"
									max="200"
									value={formData.strategyConfig.longPeriod || 20}
									onChange={e => handleStrategyConfigChange('longPeriod', parseInt(e.target.value))}
									className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-blue-500 text-sm"
									placeholder="20"
								/>
								<p className="text-xs text-gray-400 mt-1">Long-term moving average period</p>
							</div>
						</div>
					)}

					{formData.strategy === 'rsi' && (
						<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
							<div>
								<label className="block text-sm font-medium text-gray-300 mb-1">RSI Period</label>
								<input
									type="number"
									min="2"
									max="50"
									value={formData.strategyConfig.rsiPeriod || 14}
									onChange={e => handleStrategyConfigChange('rsiPeriod', parseInt(e.target.value))}
									className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-blue-500 text-sm"
									placeholder="14"
								/>
								<p className="text-xs text-gray-400 mt-1">RSI calculation period</p>
							</div>
							<div>
								<label className="block text-sm font-medium text-gray-300 mb-1">Oversold Level</label>
								<input
									type="number"
									min="10"
									max="40"
									value={formData.strategyConfig.oversoldLevel || 30}
									onChange={e => handleStrategyConfigChange('oversoldLevel', parseInt(e.target.value))}
									className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-blue-500 text-sm"
									placeholder="30"
								/>
								<p className="text-xs text-gray-400 mt-1">RSI oversold threshold</p>
							</div>
							<div>
								<label className="block text-sm font-medium text-gray-300 mb-1">Overbought Level</label>
								<input
									type="number"
									min="60"
									max="90"
									value={formData.strategyConfig.overboughtLevel || 70}
									onChange={e => handleStrategyConfigChange('overboughtLevel', parseInt(e.target.value))}
									className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-blue-500 text-sm"
									placeholder="70"
								/>
								<p className="text-xs text-gray-400 mt-1">RSI overbought threshold</p>
							</div>
						</div>
					)}

					{formData.strategy === 'macd' && (
						<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
							<div>
								<label className="block text-sm font-medium text-gray-300 mb-1">Fast Period</label>
								<input
									type="number"
									min="2"
									max="50"
									value={formData.strategyConfig.fastPeriod || 12}
									onChange={e => handleStrategyConfigChange('fastPeriod', parseInt(e.target.value))}
									className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-blue-500 text-sm"
									placeholder="12"
								/>
								<p className="text-xs text-gray-400 mt-1">Fast EMA period</p>
							</div>
							<div>
								<label className="block text-sm font-medium text-gray-300 mb-1">Slow Period</label>
								<input
									type="number"
									min="5"
									max="100"
									value={formData.strategyConfig.slowPeriod || 26}
									onChange={e => handleStrategyConfigChange('slowPeriod', parseInt(e.target.value))}
									className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-blue-500 text-sm"
									placeholder="26"
								/>
								<p className="text-xs text-gray-400 mt-1">Slow EMA period</p>
							</div>
							<div>
								<label className="block text-sm font-medium text-gray-300 mb-1">Signal Period</label>
								<input
									type="number"
									min="2"
									max="50"
									value={formData.strategyConfig.signalPeriod || 9}
									onChange={e => handleStrategyConfigChange('signalPeriod', parseInt(e.target.value))}
									className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-blue-500 text-sm"
									placeholder="9"
								/>
								<p className="text-xs text-gray-400 mt-1">Signal line EMA period</p>
							</div>
						</div>
					)}

					{formData.strategy === 'bollinger-bands' && (
						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div>
								<label className="block text-sm font-medium text-gray-300 mb-1">Period</label>
								<input
									type="number"
									min="5"
									max="100"
									value={formData.strategyConfig.period || 20}
									onChange={e => handleStrategyConfigChange('period', parseInt(e.target.value))}
									className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-blue-500 text-sm"
									placeholder="20"
								/>
								<p className="text-xs text-gray-400 mt-1">Moving average period for bands</p>
							</div>
							<div>
								<label className="block text-sm font-medium text-gray-300 mb-1">Standard Deviations</label>
								<input
									type="number"
									step="0.1"
									min="0.5"
									max="5"
									value={formData.strategyConfig.standardDeviations || 2}
									onChange={e => handleStrategyConfigChange('standardDeviations', parseFloat(e.target.value))}
									className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-blue-500 text-sm"
									placeholder="2"
								/>
								<p className="text-xs text-gray-400 mt-1">Number of standard deviations for bands</p>
							</div>
						</div>
					)}

					{formData.strategy === 'oscillate' && (
						<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
							<div>
								<label className="block text-sm font-medium text-gray-300 mb-1">Range Threshold (%)</label>
								<input
									type="number"
									step="0.1"
									min="0.1"
									max="5"
									value={formData.strategyConfig.rangeThreshold || 0.5}
									onChange={e => handleStrategyConfigChange('rangeThreshold', parseFloat(e.target.value))}
									className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-blue-500 text-sm"
									placeholder="0.5"
								/>
								<p className="text-xs text-gray-400 mt-1">Price range percentage for oscillation detection</p>
							</div>
							<div>
								<label className="block text-sm font-medium text-gray-300 mb-1">Min Oscillations</label>
								<input
									type="number"
									min="2"
									max="10"
									value={formData.strategyConfig.minOscillations || 3}
									onChange={e => handleStrategyConfigChange('minOscillations', parseInt(e.target.value))}
									className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-blue-500 text-sm"
									placeholder="3"
								/>
								<p className="text-xs text-gray-400 mt-1">Minimum oscillations to confirm pattern</p>
							</div>
							<div>
								<label className="block text-sm font-medium text-gray-300 mb-1">Confidence Threshold</label>
								<input
									type="number"
									step="0.1"
									min="0.1"
									max="1"
									value={formData.strategyConfig.confidenceThreshold || 0.7}
									onChange={e => handleStrategyConfigChange('confidenceThreshold', parseFloat(e.target.value))}
									className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-blue-500 text-sm"
									placeholder="0.7"
								/>
								<p className="text-xs text-gray-400 mt-1">Minimum confidence to execute trade</p>
							</div>
						</div>
					)}

					{formData.strategy === 'slide' && (
						<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
							<div>
								<label className="block text-sm font-medium text-gray-300 mb-1">Trend Threshold (%)</label>
								<input
									type="number"
									step="0.1"
									min="0.1"
									max="2"
									value={formData.strategyConfig.trendThreshold || 0.2}
									onChange={e => handleStrategyConfigChange('trendThreshold', parseFloat(e.target.value))}
									className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-blue-500 text-sm"
									placeholder="0.2"
								/>
								<p className="text-xs text-gray-400 mt-1">Minimum trend percentage to trade</p>
							</div>
							<div>
								<label className="block text-sm font-medium text-gray-300 mb-1">Momentum Period</label>
								<input
									type="number"
									min="5"
									max="50"
									value={formData.strategyConfig.momentumPeriod || 10}
									onChange={e => handleStrategyConfigChange('momentumPeriod', parseInt(e.target.value))}
									className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-blue-500 text-sm"
									placeholder="10"
								/>
								<p className="text-xs text-gray-400 mt-1">Periods for momentum calculation</p>
							</div>
							<div>
								<label className="block text-sm font-medium text-gray-300 mb-1">Trail Distance (%)</label>
								<input
									type="number"
									step="0.05"
									min="0.05"
									max="1"
									value={formData.strategyConfig.trailDistance || 0.15}
									onChange={e => handleStrategyConfigChange('trailDistance', parseFloat(e.target.value))}
									className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-blue-500 text-sm"
									placeholder="0.15"
								/>
								<p className="text-xs text-gray-400 mt-1">Trailing stop distance percentage</p>
							</div>
						</div>
					)}

					{formData.strategy === 'ai' && (
						<div className="space-y-4">
							<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
								<div>
									<label className="block text-sm font-medium text-gray-300 mb-1">AI Provider</label>
									<select
										value={formData.strategyConfig.aiProvider || 'openai'}
										onChange={e => handleStrategyConfigChange('aiProvider', e.target.value)}
										className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-blue-500 text-sm"
									>
										<option value="openai">OpenAI (ChatGPT)</option>
										<option value="deepseek">DeepSeek</option>
									</select>
									<p className="text-xs text-gray-400 mt-1">AI service provider</p>
								</div>
								<div>
									<label className="block text-sm font-medium text-gray-300 mb-1">Min Confidence</label>
									<input
										type="number"
										step="0.1"
										min="0.1"
										max="1"
										value={formData.strategyConfig.minConfidence || 0.7}
										onChange={e => handleStrategyConfigChange('minConfidence', parseFloat(e.target.value))}
										className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-blue-500 text-sm"
										placeholder="0.7"
									/>
									<p className="text-xs text-gray-400 mt-1">Minimum AI confidence to trade</p>
								</div>
							</div>
							<div>
								<label className="block text-sm font-medium text-gray-300 mb-1">API Key</label>
								<input
									type="password"
									value={formData.strategyConfig.apiKey || ''}
									onChange={e => handleStrategyConfigChange('apiKey', e.target.value)}
									className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-blue-500 text-sm"
									placeholder="Enter your AI API key"
								/>
								<p className="text-xs text-gray-400 mt-1">API key for the selected AI provider</p>
							</div>
							<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
								<div>
									<label className="block text-sm font-medium text-gray-300 mb-1">Analysis Interval (seconds)</label>
									<input
										type="number"
										min="30"
										max="300"
										value={formData.strategyConfig.analysisInterval || 60}
										onChange={e => handleStrategyConfigChange('analysisInterval', parseInt(e.target.value))}
										className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-blue-500 text-sm"
										placeholder="60"
									/>
									<p className="text-xs text-gray-400 mt-1">How often to request AI analysis</p>
								</div>
								<div>
									<label className="block text-sm font-medium text-gray-300 mb-1">Max Daily Trades</label>
									<input
										type="number"
										min="1"
										max="100"
										value={formData.strategyConfig.maxDailyTrades || 20}
										onChange={e => handleStrategyConfigChange('maxDailyTrades', parseInt(e.target.value))}
										className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-blue-500 text-sm"
										placeholder="20"
									/>
									<p className="text-xs text-gray-400 mt-1">Maximum trades per day</p>
								</div>
							</div>
						</div>
					)}

					{formData.strategy === 'custom' && (
						<div>
							<label className="block text-sm font-medium text-gray-300 mb-1">Custom Strategy Code</label>
							<textarea
								rows={8}
								value={formData.strategyConfig.customCode || ''}
								onChange={e => handleStrategyConfigChange('customCode', e.target.value)}
								className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-blue-500 font-mono text-sm"
								placeholder="// Write your custom trading strategy here..."
							/>
							<p className="text-xs text-gray-400 mt-1">JavaScript code for your custom trading strategy</p>
						</div>
					)}
				</div>

				{/* Basic Settings */}
				<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
					<div>
						<label className="block text-sm font-medium text-gray-300 mb-1">Trade Amount ($)</label>
						<input
							type="number"
							step="0.1"
							min="0.1"
							max="1000"
							value={formData.tradeAmount}
							onChange={e => handleInputChange('tradeAmount', parseFloat(e.target.value))}
							className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-blue-500 text-sm"
							placeholder="1"
						/>
						<p className="text-xs text-gray-400 mt-1">Amount to invest per trade</p>
					</div>

					<div className="flex items-center">
						<input
							type="checkbox"
							id="autoTrade"
							checked={formData.autoTrade}
							onChange={e => handleInputChange('autoTrade', e.target.checked)}
							className="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500"
						/>
						<label htmlFor="autoTrade" className="ml-2 text-sm font-medium text-gray-300">
							Enable Auto Trading
						</label>
					</div>
				</div>

				{/* Asset Selection Settings */}
				<div className="border-t border-gray-700 pt-4">
					<h3 className="text-base font-medium text-gray-200 mb-3">Asset Selection</h3>

					<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
						<div>
							<label className="block text-sm font-medium text-gray-300 mb-1">Asset Type</label>
							<select
								value={formData.assetType || 'currency'}
								onChange={e => handleInputChange('assetType', e.target.value)}
								className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-blue-500 text-sm"
							>
								<option value="currency">Currencies</option>
								<option value="cryptocurrency">Cryptocurrencies</option>
								<option value="commodity">Commodities</option>
								<option value="stock">Stocks</option>
								<option value="index">Indices</option>
							</select>
							<p className="text-xs text-gray-400 mt-1">Type of asset to trade</p>
						</div>

						<div>
							<label className="block text-sm font-medium text-gray-300 mb-1">Asset Filter</label>
							<select
								value={formData.assetFilter || 'ALL'}
								onChange={e => handleInputChange('assetFilter', e.target.value)}
								className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-blue-500 text-sm"
							>
								<option value="ALL">All Assets</option>
								<option value="OTC">OTC Only</option>
							</select>
							<p className="text-xs text-gray-400 mt-1">Filter assets by availability</p>
						</div>

						<div>
							<label className="block text-sm font-medium text-gray-300 mb-1">Preferred Asset</label>
							<input
								type="text"
								value={formData.asset || ''}
								onChange={e => handleInputChange('asset', e.target.value)}
								className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-blue-500 text-sm"
								placeholder="e.g., AUD/CAD, EUR/USD"
							/>
							<p className="text-xs text-gray-400 mt-1">Specific asset to trade (optional)</p>
						</div>
					</div>
				</div>

				{/* Advanced Settings */}
				<div className="border-t border-gray-700 pt-4">
					<h3 className="text-base font-medium text-gray-200 mb-3">Advanced Settings</h3>

					{/* Stop Loss Configuration */}
					<div className="mb-6">
						<div className="flex items-center mb-3">
							<input
								type="checkbox"
								id="enableStopLoss"
								checked={formData.stopLoss?.enabled || false}
								onChange={e => handleStopLossConfigChange('enabled', e.target.checked)}
								className="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500"
							/>
							<label htmlFor="enableStopLoss" className="ml-2 text-sm font-medium text-gray-300">
								Enable Stop Loss
							</label>
						</div>

						{formData.stopLoss?.enabled && (
							<div className="ml-6 space-y-4">
								<div>
									<label className="block text-sm font-medium text-gray-300 mb-2">Stop Loss Mode</label>
									<div className="flex space-x-4">
										<label className="flex items-center">
											<input
												type="radio"
												name="stopLossMode"
												value="percentage"
												checked={formData.stopLoss?.mode === 'percentage'}
												onChange={e => handleStopLossConfigChange('mode', e.target.value as StopLossMode)}
												className="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 focus:ring-blue-500"
											/>
											<span className="ml-2 text-sm text-gray-300">Percentage</span>
										</label>
										<label className="flex items-center">
											<input
												type="radio"
												name="stopLossMode"
												value="fixed"
												checked={formData.stopLoss?.mode === 'fixed'}
												onChange={e => handleStopLossConfigChange('mode', e.target.value as StopLossMode)}
												className="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 focus:ring-blue-500"
											/>
											<span className="ml-2 text-sm text-gray-300">Fixed Amount</span>
										</label>
									</div>
								</div>

								{formData.stopLoss?.mode === 'percentage' && (
									<div>
										<label className="block text-sm font-medium text-gray-300 mb-1">Stop Loss Percentage (%)</label>
										<input
											type="number"
											step="0.1"
											min="0.1"
											max="50"
											onChange={e =>
												handleStopLossConfigChange(
													'percentage',
													e.target.value ? parseFloat(e.target.value) : undefined
												)
											}
											className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-blue-500 text-sm"
											placeholder="10"
										/>
										<p className="text-xs text-gray-400 mt-1">
											Stop trading if total loss exceeds this percentage of your balance
										</p>
									</div>
								)}

								{formData.stopLoss?.mode === 'fixed' && (
									<div>
										<label className="block text-sm font-medium text-gray-300 mb-1">Stop Loss Amount ($)</label>
										<input
											type="number"
											step="1"
											min="1"
											max="10000"
											value={formData.stopLoss?.fixedAmount || 100}
											onChange={e => handleStopLossConfigChange('fixedAmount', parseFloat(e.target.value))}
											className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-blue-500 text-sm"
											placeholder="100"
										/>
										<p className="text-xs text-gray-400 mt-1">
											Stop trading if total loss exceeds this fixed dollar amount
										</p>
									</div>
								)}
							</div>
						)}
					</div>

					{/* Take Profit */}
					<div>
						<label className="block text-sm font-medium text-gray-300 mb-1">Take Profit (%)</label>
						<input
							type="number"
							step="0.1"
							min="0"
							max="100"
							value={formData.takeProfit || ''}
							onChange={e => handleInputChange('takeProfit', e.target.value ? parseFloat(e.target.value) : undefined)}
							className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-blue-500 text-sm"
							placeholder="Optional"
						/>
						<p className="text-xs text-gray-400 mt-1">Stop trading if total profit exceeds this percentage</p>
					</div>
				</div>

				{/* Screenshot Price Extraction */}
				<div className="border-t border-gray-700 pt-4">
					<h3 className="text-base font-medium text-gray-200 mb-3">Screenshot Price Extraction</h3>
					<p className="text-xs text-gray-400 mb-4">
						Enable screenshot-based price extraction as a fallback when DOM selectors fail to find price data.
					</p>

					<div className="mb-4">
						<div className="flex items-center mb-3">
							<input
								type="checkbox"
								id="enableScreenshot"
								checked={formData.screenshotConfig?.enabled || false}
								onChange={e => handleScreenshotConfigChange('enabled', e.target.checked)}
								className="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500"
							/>
							<label htmlFor="enableScreenshot" className="ml-2 text-sm font-medium text-gray-300">
								Enable Screenshot Price Extraction
							</label>
						</div>

						{formData.screenshotConfig?.enabled && (
							<div className="ml-6 space-y-4">
								<div className="flex items-center">
									<input
										type="checkbox"
										id="screenshotDebug"
										checked={formData.screenshotConfig?.debugMode || false}
										onChange={e => handleScreenshotConfigChange('debugMode', e.target.checked)}
										className="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500"
									/>
									<label htmlFor="screenshotDebug" className="ml-2 text-sm font-medium text-gray-300">
										Debug Mode (Save screenshots for analysis)
									</label>
								</div>

								<div className="bg-gray-700 rounded-lg p-4">
									<h4 className="text-sm font-medium text-gray-200 mb-2">Price Area Configuration</h4>
									<p className="text-xs text-gray-400 mb-3">
										Configure the screen area where price data is displayed. Leave empty for auto-detection.
									</p>

									<div className="mb-3">
										<button
											type="button"
											className="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded text-xs transition-colors"
											onClick={() => {
												// This would need to be implemented in the parent component
												console.log('Auto-calibration requested - implement in parent component')
											}}
										>
											Auto-Calibrate Price Area
										</button>
										<p className="text-xs text-gray-400 mt-1">
											Automatically detect the optimal price area (bot must be running)
										</p>
									</div>

									<div className="grid grid-cols-2 md:grid-cols-4 gap-3">
										<div>
											<label className="block text-xs font-medium text-gray-300 mb-1">X Position</label>
											<input
												type="number"
												min="0"
												max="2000"
												value={formData.screenshotConfig?.priceArea?.x || ''}
												onChange={e =>
													handleScreenshotConfigChange('priceArea', {
														...formData.screenshotConfig?.priceArea,
														x: e.target.value ? parseInt(e.target.value) : undefined
													})
												}
												className="w-full px-2 py-1 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:border-blue-500 text-xs"
												placeholder="Auto"
											/>
										</div>
										<div>
											<label className="block text-xs font-medium text-gray-300 mb-1">Y Position</label>
											<input
												type="number"
												min="0"
												max="2000"
												value={formData.screenshotConfig?.priceArea?.y || ''}
												onChange={e =>
													handleScreenshotConfigChange('priceArea', {
														...formData.screenshotConfig?.priceArea,
														y: e.target.value ? parseInt(e.target.value) : undefined
													})
												}
												className="w-full px-2 py-1 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:border-blue-500 text-xs"
												placeholder="Auto"
											/>
										</div>
										<div>
											<label className="block text-xs font-medium text-gray-300 mb-1">Width</label>
											<input
												type="number"
												min="50"
												max="500"
												value={formData.screenshotConfig?.priceArea?.width || ''}
												onChange={e =>
													handleScreenshotConfigChange('priceArea', {
														...formData.screenshotConfig?.priceArea,
														width: e.target.value ? parseInt(e.target.value) : undefined
													})
												}
												className="w-full px-2 py-1 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:border-blue-500 text-xs"
												placeholder="Auto"
											/>
										</div>
										<div>
											<label className="block text-xs font-medium text-gray-300 mb-1">Height</label>
											<input
												type="number"
												min="20"
												max="200"
												value={formData.screenshotConfig?.priceArea?.height || ''}
												onChange={e =>
													handleScreenshotConfigChange('priceArea', {
														...formData.screenshotConfig?.priceArea,
														height: e.target.value ? parseInt(e.target.value) : undefined
													})
												}
												className="w-full px-2 py-1 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:border-blue-500 text-xs"
												placeholder="Auto"
											/>
										</div>
									</div>
								</div>
							</div>
						)}
					</div>
				</div>

				{/* Warning Notice */}
				<div className="bg-yellow-900 border border-yellow-700 rounded-lg p-3">
					<div className="flex">
						<div className="flex-shrink-0">
							<svg className="h-4 w-4 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
								<path
									fillRule="evenodd"
									d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
									clipRule="evenodd"
								/>
							</svg>
						</div>
						<div className="ml-2">
							<h3 className="text-sm font-medium text-yellow-400">Trading Risk Warning</h3>
							<div className="mt-1 text-xs text-yellow-300">
								<p>
									Trading involves significant risk. Only trade with money you can afford to lose. This bot is for
									educational purposes and past performance does not guarantee future results.
								</p>
							</div>
						</div>
					</div>
				</div>

				{/* Action Buttons */}
				<div className="flex justify-end space-x-3 pt-4 border-t border-gray-700">
					<button
						type="button"
						onClick={onCancel}
						className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors text-sm"
					>
						Cancel
					</button>
					<button
						type="submit"
						className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors text-sm"
					>
						Save Settings
					</button>
				</div>
			</form>
		</div>
	)
}
