import { useState, useEffect, useRef } from 'react'
import { TradingDashboard } from './components/TradingDashboard'
import { CompactTradingDashboard } from './components/CompactTradingDashboard'
import { SettingsPanel } from './components/SettingsPanel'
import type { TradingSettings, BotStatus, PriceData, StopLossConfig, TradeResult } from '../../../shared/types'

function App() {
	const [settings, setSettings] = useState<TradingSettings>({
		strategy: 'threshold',
		strategyConfig: {
			threshold: 0.02
		},
		tradeAmount: 1,
		autoTrade: false,
		stopLoss: {
			enabled: false,
			mode: 'percentage',
			percentage: 10
		}
	})

	const [botStatus, setBotStatus] = useState<BotStatus>({
		isRunning: false,
		currentPrice: 0,
		tradesCount: 0,
		winCount: 0,
		lossCount: 0,
		winRate: 0,
		totalProfit: 0,
		accountBalance: 0,
		lastTrade: null
	})

	const [currentPrice, setCurrentPrice] = useState<PriceData | null>(null)
	const [recentTrades, setRecentTrades] = useState<TradeResult[]>([])
	const [showSettings, setShowSettings] = useState(false)
	const [compactView, setCompactView] = useState(true) // Default to compact view
	const [statusMessage, setStatusMessage] = useState<string>('')
	const [browserStatus, setBrowserStatus] = useState<{ initialized: boolean; loggedIn: boolean }>({
		initialized: false,
		loggedIn: false
	})

	// Use refs to avoid stale closures in event listeners
	const currentPriceRef = useRef(currentPrice)
	const settingsRef = useRef(settings)
	const botStatusRef = useRef(botStatus)

	// Update refs when state changes
	useEffect(() => {
		currentPriceRef.current = currentPrice
	}, [currentPrice])

	useEffect(() => {
		settingsRef.current = settings
	}, [settings])

	useEffect(() => {
		botStatusRef.current = botStatus
	}, [botStatus])

	// Load settings and browser status on component mount
	useEffect(() => {
		const loadSettings = async () => {
			try {
				const savedSettings = await window.electronAPI.getSettings()
				setSettings(savedSettings)
			} catch (error) {
				console.error('Failed to load settings:', error)
			}
		}

		const loadBrowserStatus = async () => {
			try {
				const status = await window.electronAPI.getBrowserStatus()
				setBrowserStatus(status)
			} catch (error) {
				console.error('Failed to load browser status:', error)
			}
		}

		loadSettings()
		loadBrowserStatus()
	}, [])

	// Set up event listeners
	useEffect(() => {
		// Bot status updates
		window.electronAPI.onBotStatusUpdate((status: BotStatus) => {
			setBotStatus(status)
		})

		// Price updates
		window.electronAPI.onPriceUpdate((price: number) => {
			// Convert the price number to a PriceData object
			const previousPrice = currentPriceRef.current?.current || 0
			const priceData: PriceData = {
				current: price,
				previous: previousPrice,
				timestamp: Date.now(),
				trend: price > previousPrice ? 'up' : price < previousPrice ? 'down' : 'neutral',
				change: price - previousPrice,
				changePercent: previousPrice > 0 ? ((price - previousPrice) / previousPrice) * 100 : 0
			}
			setCurrentPrice(priceData)
		})

		// Trade results
		window.electronAPI.onTradeResult((result: TradeResult) => {
			setRecentTrades(prev => [result, ...prev.slice(0, 9)]) // Keep last 10 trades
		})

		// Stop loss triggered
		window.electronAPI.onStopLossTriggered(async (data: { totalProfit: number; stopLossConfig?: StopLossConfig }) => {
			console.log('Stop loss triggered:', data)
			const totalProfit = data?.totalProfit ?? 0
			setStatusMessage(
				`⚠️ Stop Loss Triggered! Total loss: ${Math.abs(totalProfit).toFixed(2)}. Auto trading has been disabled.`
			)
			// Update settings to reflect that auto trading is now disabled
			setSettings(prev => ({ ...prev, autoTrade: false }))

			// Persist & sync change using current refs
			const currentSettings = settingsRef.current
			const currentBotStatus = botStatusRef.current

			try {
				await window.electronAPI.saveSettings({ ...currentSettings, autoTrade: false })
				if (currentBotStatus.isRunning) {
					await window.electronAPI.updateBotSettings({ ...currentSettings, autoTrade: false })
				}
			} catch (e) {
				console.error('Failed to persist stop-loss change', e)
				window.electronAPI.showErrorDialog('Error', `Failed to update settings after stop-loss: ${e}`)
			}
		})

		// Bot status messages
		window.electronAPI.onBotStatusMessage((message: string) => {
			setStatusMessage(message)
			// Clear message after 10 seconds
			setTimeout(() => setStatusMessage(''), 10000)
		})

		// Cleanup listeners on unmount
		return () => {
			window.electronAPI.removeAllListeners('bot-status-update')
			window.electronAPI.removeAllListeners('price-update')
			window.electronAPI.removeAllListeners('trade-result')
			window.electronAPI.removeAllListeners('stop-loss-triggered')
			window.electronAPI.removeAllListeners('bot-status-message')
		}
	}, [])

	const handleStartBot = async () => {
		try {
			const result = await window.electronAPI.startBot(settings)
			if (result.success) {
				window.electronAPI.showInfoDialog('Success', result.message)
			} else {
				window.electronAPI.showErrorDialog('Error', result.message)
			}
		} catch (error) {
			window.electronAPI.showErrorDialog('Error', `Failed to start bot: ${error}`)
		}
	}

	const handleStopBot = async () => {
		try {
			const result = await window.electronAPI.stopBot()
			if (result.success) {
				window.electronAPI.showInfoDialog('Success', result.message)
			} else {
				window.electronAPI.showErrorDialog('Error', result.message)
			}
		} catch (error) {
			window.electronAPI.showErrorDialog('Error', `Failed to stop bot: ${error}`)
		}
	}

	const handleInitializeBrowser = async () => {
		try {
			const result = await window.electronAPI.initializeBrowser()
			if (result.success) {
				setBrowserStatus({ initialized: true, loggedIn: false })
				window.electronAPI.showInfoDialog('Success', result.message)
				// Check login status after initialization
				setTimeout(async () => {
					const loginStatus = await window.electronAPI.checkLoginStatus()
					setBrowserStatus(prev => ({ ...prev, loggedIn: loginStatus.loggedIn }))
				}, 2000)
			} else {
				window.electronAPI.showErrorDialog('Error', result.message)
			}
		} catch (error) {
			window.electronAPI.showErrorDialog('Error', `Failed to initialize browser: ${error}`)
		}
	}

	const handleCheckLoginStatus = async () => {
		try {
			const result = await window.electronAPI.checkLoginStatus()
			setBrowserStatus(prev => ({ ...prev, loggedIn: result.loggedIn }))
			if (result.loggedIn) {
				window.electronAPI.showInfoDialog('Login Status', 'You are logged in to Pocket Option')
			} else {
				window.electronAPI.showInfoDialog('Login Status', 'Please log in to Pocket Option')
			}
		} catch (error) {
			window.electronAPI.showErrorDialog('Error', `Failed to check login status: ${error}`)
		}
	}

	const handleCloseBrowser = async () => {
		try {
			const result = await window.electronAPI.closeBrowser()
			if (result.success) {
				setBrowserStatus({ initialized: false, loggedIn: false })
				window.electronAPI.showInfoDialog('Success', result.message)
			} else {
				window.electronAPI.showErrorDialog('Error', result.message)
			}
		} catch (error) {
			window.electronAPI.showErrorDialog('Error', `Failed to close browser: ${error}`)
		}
	}

	const handleSaveSettings = async (newSettings: TradingSettings) => {
		try {
			await window.electronAPI.saveSettings(newSettings)
			setSettings(newSettings)

			// Update bot settings if running
			if (botStatus.isRunning) {
				await window.electronAPI.updateBotSettings(newSettings)
			}

			setShowSettings(false)
			window.electronAPI.showInfoDialog('Success', 'Settings saved successfully')
		} catch (error) {
			window.electronAPI.showErrorDialog('Error', `Failed to save settings: ${error}`)
		}
	}

	// TODO: Remove this on production
	const handleDebugPriceElements = async () => {
		try {
			await window.electronAPI.debugPriceElements()
		} catch (error) {
			window.electronAPI.showErrorDialog('Error', `Failed to debug price elements: ${error}`)
		}
	}

	// TODO: Remove this on production
	const handleSelectTradingAsset = async () => {
		try {
			await window.electronAPI.selectTradingAsset()
		} catch (error) {
			window.electronAPI.showErrorDialog('Error', `Failed to select trading asset: ${error}`)
		}
	}

	const handleUpdateSettings = async (newSettings: Partial<TradingSettings>) => {
		const updatedSettings = { ...settings, ...newSettings }
		try {
			await window.electronAPI.saveSettings(updatedSettings)
			setSettings(updatedSettings)

			// Update bot settings if running
			if (botStatus.isRunning) {
				await window.electronAPI.updateBotSettings(updatedSettings)
			}
		} catch (error) {
			window.electronAPI.showErrorDialog('Error', `Failed to update settings: ${error}`)
		}
	}

	return (
		<div className="min-h-screen bg-gray-900 text-white">
			<div className={`container mx-auto px-3 py-4 ${compactView ? 'max-w-md' : ''}`}>
				{!compactView && (
					<header className="flex justify-between items-center mb-4">
						<h1 className="text-2xl font-bold text-blue-400">Pocket Option Trading Bot</h1>
						<div className="flex gap-2">
							<button
								onClick={() => setCompactView(!compactView)}
								className="px-3 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg transition-colors text-sm"
							>
								{compactView ? 'Full View' : 'Compact'}
							</button>
							<button
								onClick={() => setShowSettings(!showSettings)}
								className="px-3 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors text-sm"
							>
								Settings
							</button>
						</div>
					</header>
				)}

				{showSettings ? (
					<SettingsPanel settings={settings} onSave={handleSaveSettings} onCancel={() => setShowSettings(false)} />
				) : compactView ? (
					<CompactTradingDashboard
						botStatus={botStatus}
						currentPrice={currentPrice}
						recentTrades={recentTrades}
						onStartBot={handleStartBot}
						onStopBot={handleStopBot}
						statusMessage={statusMessage}
						browserStatus={browserStatus}
						onInitializeBrowser={handleInitializeBrowser}
						onCheckLoginStatus={handleCheckLoginStatus}
						onCloseBrowser={handleCloseBrowser}
						onUpdateSettings={handleUpdateSettings}
						currentSettings={settings}
					/>
				) : (
					<TradingDashboard
						botStatus={botStatus}
						currentPrice={currentPrice}
						recentTrades={recentTrades}
						onStartBot={handleStartBot}
						onStopBot={handleStopBot}
						statusMessage={statusMessage}
						browserStatus={browserStatus}
						onInitializeBrowser={handleInitializeBrowser}
						onCheckLoginStatus={handleCheckLoginStatus}
						onCloseBrowser={handleCloseBrowser}
						// TODO: Remove these on production
						onDebugPriceElements={handleDebugPriceElements}
						onSelectTradingAsset={handleSelectTradingAsset}
					/>
				)}

				{/* Compact view toggle button */}
				{compactView && (
					<div className="fixed bottom-4 right-4 flex gap-2">
						<button
							onClick={() => setCompactView(false)}
							className="px-3 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg transition-colors text-sm shadow-lg"
						>
							Full View
						</button>
						<button
							onClick={() => setShowSettings(!showSettings)}
							className="px-3 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors text-sm shadow-lg"
						>
							⚙️
						</button>
					</div>
				)}
			</div>
		</div>
	)
}

export default App
