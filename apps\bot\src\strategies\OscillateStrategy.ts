import { PriceData } from '../../../../shared/types'
import { TradingStrategy, TradingDecision } from './TradingStrategy'

interface OscillateConfig {
	rangeThreshold: number // Percentage range to consider as oscillation
	minOscillations: number // Minimum number of oscillations to confirm pattern
	breakoutThreshold: number // Percentage move to consider breakout
	timeWindow: number // Time window in minutes to analyze
	confidenceThreshold: number // Minimum confidence to trade
	maxPositions: number // Maximum concurrent positions
}

interface OscillationRange {
	upper: number
	lower: number
	middle: number
	strength: number // 0-1, how well defined the range is
	duration: number // How long the range has been active
	touchCount: number // Number of times price touched boundaries
}

export class OscillateStrategy extends TradingStrategy {
	private readonly COOLDOWN_PERIOD = 30000 // 30 seconds between trades
	private lastTradeTime = 0
	private currentRange: OscillationRange | null = null
	private rangeHistory: OscillationRange[] = []
	private activePositions = 0

	async evaluate(priceData: PriceData): Promise<TradingDecision> {
		// Add current price data to history
		this.addPriceData(priceData)

		// Configuration with defaults
		const config: OscillateConfig = {
			rangeThreshold: this.config.rangeThreshold || 0.5, // 0.5% range
			minOscillations: this.config.minOscillations || 3,
			breakoutThreshold: this.config.breakoutThreshold || 0.3, // 0.3% breakout
			timeWindow: this.config.timeWindow || 15, // 15 minutes
			confidenceThreshold: this.config.confidenceThreshold || 0.7,
			maxPositions: this.config.maxPositions || 2
		}

		// Check cooldown period
		const now = Date.now()
		if (now - this.lastTradeTime < this.COOLDOWN_PERIOD) {
			return {
				shouldTrade: false,
				reason: `Cooldown period active (${Math.round((this.COOLDOWN_PERIOD - (now - this.lastTradeTime)) / 1000)}s remaining)`
			}
		}

		// Check position limits
		if (this.activePositions >= config.maxPositions) {
			return {
				shouldTrade: false,
				reason: `Maximum positions reached (${this.activePositions}/${config.maxPositions})`
			}
		}

		// Need sufficient data
		if (this.priceHistory.length < 20) {
			return {
				shouldTrade: false,
				reason: `Insufficient data for oscillation analysis (need 20+ points, have ${this.priceHistory.length})`
			}
		}

		// Analyze current oscillation pattern
		const analysis = this.analyzeOscillation(config)
		
		if (!analysis.range) {
			return {
				shouldTrade: false,
				reason: 'No clear oscillation range detected'
			}
		}

		// Generate trading decision
		const decision = this.generateOscillationDecision(priceData, analysis, config)

		// Update last trade time if we're making a trade
		if (decision.shouldTrade) {
			this.lastTradeTime = now
			this.activePositions++
		}

		return decision
	}

	private analyzeOscillation(config: OscillateConfig): { range: OscillationRange | null; pattern: string } {
		const timeWindowMs = config.timeWindow * 60 * 1000
		const cutoffTime = Date.now() - timeWindowMs
		
		// Filter recent price data
		const recentPrices = this.priceHistory.filter(p => p.timestamp >= cutoffTime)
		
		if (recentPrices.length < 10) {
			return { range: null, pattern: 'insufficient_data' }
		}

		const prices = recentPrices.map(p => p.current)
		const high = Math.max(...prices)
		const low = Math.min(...prices)
		const range = (high - low) / low * 100

		// Check if range is within oscillation threshold
		if (range > config.rangeThreshold * 2) {
			return { range: null, pattern: 'too_volatile' }
		}

		if (range < config.rangeThreshold * 0.3) {
			return { range: null, pattern: 'too_tight' }
		}

		// Analyze oscillation quality
		const middle = (high + low) / 2
		const oscillationRange: OscillationRange = {
			upper: high,
			lower: low,
			middle,
			strength: this.calculateRangeStrength(prices, high, low),
			duration: recentPrices.length,
			touchCount: this.countBoundaryTouches(prices, high, low)
		}

		// Validate oscillation pattern
		if (oscillationRange.strength < 0.6 || oscillationRange.touchCount < config.minOscillations) {
			return { range: null, pattern: 'weak_oscillation' }
		}

		this.currentRange = oscillationRange
		return { range: oscillationRange, pattern: 'valid_oscillation' }
	}

	private calculateRangeStrength(prices: number[], high: number, low: number): number {
		const range = high - low
		const middle = (high + low) / 2
		
		// Calculate how well prices respect the boundaries
		let boundaryRespect = 0
		let middleReversals = 0
		
		for (let i = 1; i < prices.length - 1; i++) {
			const price = prices[i]
			const prevPrice = prices[i - 1]
			const nextPrice = prices[i + 1]
			
			// Check boundary respect
			if (price >= high * 0.98 && nextPrice < price) boundaryRespect++
			if (price <= low * 1.02 && nextPrice > price) boundaryRespect++
			
			// Check middle reversals
			if (Math.abs(price - middle) < range * 0.1) {
				if ((prevPrice > middle && nextPrice < middle) || (prevPrice < middle && nextPrice > middle)) {
					middleReversals++
				}
			}
		}
		
		const maxPossibleBoundaryTouches = Math.floor(prices.length / 3)
		const boundaryScore = Math.min(1, boundaryRespect / maxPossibleBoundaryTouches)
		const reversalScore = Math.min(1, middleReversals / 3)
		
		return (boundaryScore * 0.7 + reversalScore * 0.3)
	}

	private countBoundaryTouches(prices: number[], high: number, low: number): number {
		let touches = 0
		const upperThreshold = high * 0.995
		const lowerThreshold = low * 1.005
		
		for (const price of prices) {
			if (price >= upperThreshold || price <= lowerThreshold) {
				touches++
			}
		}
		
		return touches
	}

	private generateOscillationDecision(
		priceData: PriceData,
		analysis: { range: OscillationRange; pattern: string },
		config: OscillateConfig
	): TradingDecision {
		const { range } = analysis
		const currentPrice = priceData.current
		
		// Calculate position within range
		const rangeSize = range.upper - range.lower
		const positionInRange = (currentPrice - range.lower) / rangeSize
		
		// Determine trade direction based on position and momentum
		let direction: 'high' | 'low' | null = null
		let confidence = 0
		let reasoning = ''

		// Near upper boundary - expect reversal down
		if (positionInRange >= 0.85) {
			direction = 'low'
			confidence = 0.7 + (positionInRange - 0.85) * 2 // Higher confidence closer to boundary
			reasoning = `Price near upper boundary (${(positionInRange * 100).toFixed(1)}% of range), expecting reversal down`
		}
		// Near lower boundary - expect reversal up
		else if (positionInRange <= 0.15) {
			direction = 'high'
			confidence = 0.7 + (0.15 - positionInRange) * 2
			reasoning = `Price near lower boundary (${(positionInRange * 100).toFixed(1)}% of range), expecting reversal up`
		}
		// In middle zone - wait for clearer signal
		else {
			// Check for momentum towards boundaries
			const recentTrend = this.calculateRecentTrend(5)
			
			if (recentTrend.strength > 0.3) {
				if (recentTrend.direction === 'up' && positionInRange > 0.6) {
					direction = 'low'
					confidence = 0.6
					reasoning = `Upward momentum in upper half of range, expecting reversal`
				} else if (recentTrend.direction === 'down' && positionInRange < 0.4) {
					direction = 'high'
					confidence = 0.6
					reasoning = `Downward momentum in lower half of range, expecting reversal`
				}
			}
		}

		// Adjust confidence based on range quality
		if (direction && confidence > 0) {
			confidence *= range.strength
			
			// Boost confidence for well-established ranges
			if (range.duration > 30 && range.touchCount > 5) {
				confidence *= 1.2
			}
			
			// Reduce confidence for new ranges
			if (range.duration < 10) {
				confidence *= 0.8
			}
			
			confidence = Math.min(1, confidence)
		}

		// Check if confidence meets threshold
		if (!direction || confidence < config.confidenceThreshold) {
			return {
				shouldTrade: false,
				reason: reasoning || `Confidence ${(confidence * 100).toFixed(1)}% below threshold ${(config.confidenceThreshold * 100).toFixed(1)}%`
			}
		}

		return {
			shouldTrade: true,
			direction,
			confidence,
			reason: reasoning,
			metadata: {
				strategy: 'oscillate',
				rangePosition: positionInRange,
				rangeStrength: range.strength,
				rangeDuration: range.duration,
				boundaryTouches: range.touchCount
			}
		}
	}

	private calculateRecentTrend(periods: number): { direction: 'up' | 'down' | 'neutral'; strength: number } {
		if (this.priceHistory.length < periods) {
			return { direction: 'neutral', strength: 0 }
		}

		const recentPrices = this.priceHistory.slice(-periods).map(p => p.current)
		const firstPrice = recentPrices[0]
		const lastPrice = recentPrices[recentPrices.length - 1]
		
		const change = (lastPrice - firstPrice) / firstPrice
		const direction = change > 0 ? 'up' : change < 0 ? 'down' : 'neutral'
		const strength = Math.abs(change) * 100 // Convert to percentage
		
		return { direction, strength: Math.min(1, strength) }
	}

	// Method to be called when a position closes
	onPositionClose(): void {
		this.activePositions = Math.max(0, this.activePositions - 1)
	}

	// Get current range information for UI display
	getCurrentRange(): OscillationRange | null {
		return this.currentRange
	}

	// Get strategy status
	getStatus(): any {
		return {
			activePositions: this.activePositions,
			currentRange: this.currentRange,
			lastTradeTime: this.lastTradeTime,
			cooldownRemaining: Math.max(0, this.COOLDOWN_PERIOD - (Date.now() - this.lastTradeTime))
		}
	}
}
